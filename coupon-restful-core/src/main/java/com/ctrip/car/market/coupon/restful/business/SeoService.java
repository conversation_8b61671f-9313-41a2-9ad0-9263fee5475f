package com.ctrip.car.market.coupon.restful.business;

import com.ctrip.car.commodity.common.service.types.SimpleStandardProductNameDTO;
import com.ctrip.car.commodity.vendor.query.service.method.QueryVendorListToCacheResponseType;
import com.ctrip.car.market.common.cache.annotations.NCache;
import com.ctrip.car.market.coupon.restful.cache.*;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoFaqRequestType;
import com.ctrip.car.market.coupon.restful.contract.seo.VendorVehicleDto;
import com.ctrip.car.market.coupon.restful.dto.CityPoiConfigItem;
import com.ctrip.car.market.coupon.restful.dto.VendorCityDto;
import com.ctrip.car.market.coupon.restful.dto.VendorCityItem;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.carcommodity.CarCommodityCommonServiceProxy;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.osdshopping.BasicDataProxy;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.osdshopping.TmsProxy;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.poi.GeoProxy;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.seoplatform.SeoPlatformProxy;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.vendor.VendorProxy;
import com.ctrip.car.market.coupon.restful.qconfig.TripConfig;
import com.ctrip.car.market.coupon.restful.utils.CurrencyUtil;
import com.ctrip.car.market.job.common.entity.seo.*;
import com.ctrip.car.osd.basicdataservice.dto.GetAirportsResponseType;
import com.ctrip.car.osd.shopping.api.entity.QueryRecomdProductsResponseType;
import com.ctrip.corp.foundation.common.enums.LanguageLocaleEnum;
import com.ctrip.corp.foundation.translation.currency.enums.CurrencyEnum;
import com.ctrip.dcs.geo.domain.repository.CityRepository;
import com.ctrip.dcs.geo.domain.repository.CountryRepository;
import com.ctrip.dcs.geo.domain.repository.ProvinceRepository;
import com.ctrip.dcs.geo.domain.value.City;
import com.ctrip.dcs.geo.domain.value.Country;
import com.ctrip.dcs.geo.domain.value.Province;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.ibu.platform.shark.sdk.api.L10n;
import com.ctrip.igt.geo.interfaces.dto.PlaceDetailsDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class SeoService {

    private final ILog logger = LogManager.getLogger(SeoService.class);

    @Resource
    private SeoCountryCache seoCountryCache;

    @Resource
    private SeoCityCache seoCityCache;

    @Resource
    private SeoInformationCache seoInformationCache;

    @Resource
    private SeoPoiCache seoPoiCache;

    @Resource
    private CountryRepository countryRepository;

    @Resource
    private CityRepository cityRepository;

    @Resource
    private ProvinceRepository provinceRepository;

    @Resource
    private BasicDataProxy basicDataProxy;

    @Resource
    private TmsProxy tmsProxy;

    @Resource
    private VendorProxy vendorProxy;

    @Resource
    private TripConfig tripConfig;

    @Resource
    private SeoPlatformProxy seoPlatformProxy;

    @Resource
    private GeoProxy geoProxy;

    @Resource
    private SeoVendorCache seoVendorCache;

    @Resource
    private CarCommodityCommonServiceProxy carCommodityCommonServiceProxy;

    public String getSiteUrl(String url, String locale) {
        if (StringUtils.isEmpty(url)) {
            return null;
        }
        String site = "us";
        if (StringUtils.isNotEmpty(locale)) {
            if (locale.equalsIgnoreCase("en-XX")) {
                site = "www";
            } else if (locale.contains("-")) {
                site = locale.substring(locale.indexOf("-") + 1).toLowerCase();
            }
        }
        site = StringUtils.isEmpty(site) ? "us" : site;
        return url.replaceAll("www", site);
    }

    public String currencyString(BigDecimal price, LanguageLocaleEnum llEnum, CurrencyEnum currEnum) {
        try {
            if (llEnum == null) {
                llEnum = LanguageLocaleEnum.EN_US;
            }
            return CurrencyUtil.getPriceWithCurrency(price, llEnum.getLanguageLocaleString(), currEnum.getCurrency());
        } catch (Exception e) {
            logger.warn("currencyString", price.toString() + ":" + llEnum.getLanguageString() + ":" + currEnum.getCurrency());
            return null;
        }
    }

    public String getCountryName(Integer countryId, String locale) {
        try {
            String countryName = seoPlatformProxy.querySeoName(1, locale, countryId.toString());
            if (StringUtils.isNotEmpty(countryName)) {
                return countryName;
            }
            Country country = countryRepository.findOne(countryId.longValue(), locale);
            if (country != null) {
                return country.getTranslationName();
            }
            logger.warn("getCountryName_error", countryId + "_" + locale);
            return "";
        } catch (Exception e) {
            logger.warn("getCountryName_error", e.toString());
            return "";
        }
    }

    public String getProvinceName(Integer provinceId, String locale) {
        String provinceName = seoPlatformProxy.querySeoName(2, locale, provinceId.toString());
        if (StringUtils.isNotEmpty(provinceName)) {
            return provinceName;
        }
        Province province = provinceRepository.findOne(provinceId.longValue(), locale);
        if (province != null) {
            return province.getTranslationName();
        }
        logger.warn("getProvince_error", provinceId + "_" + locale);
        return "";
    }

    public String getCityName(Integer cityId, String locale) {
        String cityName = seoPlatformProxy.querySeoName(3, locale, cityId.toString());
        if (StringUtils.isNotEmpty(cityName)) {
            return cityName;
        }
        City city = cityRepository.findOne(cityId.longValue(), locale);
        if (city != null) {
            return city.getTranslationName();
        }
        logger.warn("getCityName_error", cityId + "_" + locale);
        return "";
    }

    public City getCity(Integer cityId) {
        return cityRepository.findOne(cityId.longValue());
    }

    public City getCity(Integer cityId, String locale) {
        return cityRepository.findOne(cityId.longValue(), locale);
    }

    public Map<Long, City> getCity(List<Long> cityIds, String locale) {
        try {
            Map<Long, City> result = cityRepository.findMany(cityIds, locale);
            if (result == null || result.isEmpty()) {
                logger.warn("getCityName_error", StringUtils.join(cityIds, ",") + "_" + locale);
                return Maps.newHashMap();
            }
            return result;
        } catch (Exception e) {
            logger.warn("getCity", e.toString());
            return Maps.newHashMap();
        }
    }

    public String getAirportName(String airportCode, String locale) {
        String airportName = seoPlatformProxy.querySeoName(4, locale, airportCode);
        if (StringUtils.isNotEmpty(airportName)) {
            return airportName;
        }
        GetAirportsResponseType responseType = basicDataProxy.getAirport(airportCode, locale);
        if (responseType != null && CollectionUtils.isNotEmpty(responseType.getAirports())) {
            return responseType.getAirports().get(0).getAirportName();
        }
        logger.warn("getAirportName_error", airportCode + "_" + locale);
        return "";
    }

    @NCache(holdMinute = 60, reHoldMinute = 30)
    public String getVendorName(String vendorCode) {
        QueryVendorListToCacheResponseType response = vendorProxy.queryVendor(vendorCode);
        return Objects.nonNull(response) && CollectionUtils.isNotEmpty(response.getList()) ? response.getList().get(0).getVendorName() : null;
    }

    public String getVehicleGroupName(String vehicleGroupId, String vehicleGroupName, String locale) {
        String key = tripConfig.getVehicleGroupKey() + vehicleGroupId;
        String value = tmsProxy.getTranslateValue(key, locale);
        return StringUtils.isEmpty(value) ? vehicleGroupName : value;
    }

    public List<SeoHotDestinatioinfoDO> queryHotDestination(Integer countryId, Integer cityId, Integer poiType, String poiCode) {
        //poi
        if (poiType != null && StringUtils.isNotEmpty(poiCode)) {
            return queryDestinationByPoi(poiType, poiCode);
        }
        //city
        if (Optional.ofNullable(cityId).orElse(0) > 0) {
            return queryDestinationByCity(cityId);
        }
        //country
        if (Optional.ofNullable(countryId).orElse(0) > 0) {
            return queryDestinationByCountry(countryId);
        }
        return null;
    }

    public SeoHotDestinatioinfoDO queryHotDestinationFirst(Integer countryId, Integer cityId, Integer poiType, String poiCode) {
        List<SeoHotDestinatioinfoDO> list = queryHotDestination(countryId, cityId, poiType, poiCode);
        if (CollectionUtils.isEmpty(list)) {
            //读取自定义配置
            return queryCityDefaultAirport(poiCode, cityId);
        }
        return list.stream().filter(l -> l.getOrderNum() != null && StringUtils.isNotEmpty(l.getPoiCode()))
                .max(Comparator.comparing(SeoHotDestinatioinfoDO::getOrderNum)).orElse(null);
    }

    public SeoHotCountryinfoDO queryHotCountryByCountry(Integer countryId) {
        try {
            return seoCountryCache.queryByCountry(countryId);
        } catch (Exception e) {
            logger.error("queryHotCountryByCountry", e);
            return null;
        }
    }


    public List<SeoHotCityinfoDO> queryHotCity(Integer countryId) {
        try {
            return seoCityCache.queryByCountry(countryId);
        } catch (Exception e) {
            logger.error("queryHotCity", e);
            return null;
        }
    }


    public SeoHotCityinfoDO queryHotCityByCity(Integer cityId) {
        try {
            return seoCityCache.queryByCity(cityId);
        } catch (Exception e) {
            logger.error("queryHotCityByCity", e);
            return null;
        }
    }


    private List<SeoHotDestinatioinfoDO> queryDestinationByCountry(Integer countryId) {
        try {
            return seoPoiCache.queryByCountry(countryId);
        } catch (Exception e) {
            logger.error("queryDestinationByCountry", e);
            return null;
        }
    }


    private List<SeoHotDestinatioinfoDO> queryDestinationByCity(Integer cityId) {
        try {
            return seoPoiCache.queryByCity(cityId);
        } catch (Exception e) {
            logger.error("queryDestinationByCity", e);
            return null;
        }
    }

    private List<SeoHotDestinatioinfoDO> queryDestinationByPoi(Integer poiType, String poiCode) {
        try {
            return seoPoiCache.queryByPoiCode(poiType, poiCode);
        } catch (Exception e) {
            logger.error("queryDestinationByPoi", e);
            return null;
        }
    }

    public SeoHotInformationDO queryInformationByPoi(Integer poiType, String poiCode) {
        try {
            return seoInformationCache.queryInformation(poiType, poiCode);
        } catch (Exception e) {
            logger.error("queryInformationByPoi", e);
            return null;
        }
    }

    public Map<Integer, Long> getCityOrderNum(List<SeoHotDestinatioinfoDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Maps.newHashMap();
        }
        Map<Integer, List<SeoHotDestinatioinfoDO>> cityMap = list.stream().collect(Collectors.groupingBy(SeoHotDestinatioinfoDO::getCityId));
        Map<Integer, Long> result = Maps.newHashMap();
        for (Map.Entry<Integer, List<SeoHotDestinatioinfoDO>> kv : cityMap.entrySet()) {
            result.put(kv.getKey(), kv.getValue().stream().mapToLong(SeoHotDestinatioinfoDO::getOrderNum).sum() * -1);
        }
        return result;
    }

    public String commentDateFormat(Long time, String locale) {
        try {
            Date date = new Date(time);
            L10n.DateTimeFormatter format = L10n.dateTimeFormatter(locale);
            String dateStr = format.ymdShortString(date);
            return dateStr;
        } catch (Exception e) {
            return time.toString();
        }
    }

    public String queryPoiName(Integer poiType, String poiCode, String locale) {
        if (Objects.equals(poiType, 1)) {
            return getAirportName(poiCode, locale);
        }
        if (NumberUtils.isNumber(poiCode)) {
            PlaceDetailsDTO placeDetailsDTO = geoProxy.getPoiDetail(poiCode, locale);
            return placeDetailsDTO == null ? "" : placeDetailsDTO.getName();
        }
        return "";
    }

    public PlaceDetailsDTO queryCityDefaultPoi(String poiCode, Integer cityId, String locale) {
        if (StringUtils.isNotEmpty(poiCode) || Optional.ofNullable(cityId).orElse(0) <= 0) {
            return null;
        }
        CityPoiConfigItem item = tripConfig.getCityPoiConfigList().stream().filter(l -> Objects.equals(l.getCityId(), cityId)).findFirst().orElse(null);
        if (item == null || StringUtils.isEmpty(item.getPoiCode()) || !NumberUtils.isNumber(item.getPoiCode())) {
            return null;
        }
        PlaceDetailsDTO result = geoProxy.getPoiDetail(item.getPoiCode(), locale);
        if (result != null) {
            result.setType(item.getPoiType().toString());
        }
        return result;
    }

    public SeoHotDestinatioinfoDO queryCityDefaultAirport(String poiCode, Integer cityId) {
        if (StringUtils.isNotEmpty(poiCode) || Optional.ofNullable(cityId).orElse(0) <= 0) {
            return null;
        }
        CityPoiConfigItem item = tripConfig.getCityPoiConfigList().stream().filter(l -> Objects.equals(l.getPoiType(), 1) && Objects.equals(l.getCityId(), cityId)).findFirst().orElse(null);
        if (item == null || StringUtils.isEmpty(item.getPoiCode())) {
            return null;
        }
        SeoHotDestinatioinfoDO airport = new SeoHotDestinatioinfoDO();
        airport.setPoiType(item.getPoiType());
        airport.setPoiCode(item.getPoiCode());
        airport.setCityId(cityId);
        airport.setOrderNum(0);
        City city = cityRepository.findOne(cityId.longValue());
        airport.setCountryId(city != null ? city.getCountryId().intValue() : 0);
        return airport;
    }

    public SeoHotDestinatioinfoDO queryCityDefaultPoi(String poiCode, Integer cityId) {
        if (StringUtils.isNotEmpty(poiCode) || Optional.ofNullable(cityId).orElse(0) <= 0) {
            return null;
        }
        CityPoiConfigItem item = tripConfig.getCityPoiConfigList().stream().filter(l -> Objects.equals(l.getCityId(), cityId)).findFirst().orElse(null);
        if (item == null || StringUtils.isEmpty(item.getPoiCode())) {
            return null;
        }
        SeoHotDestinatioinfoDO poi = new SeoHotDestinatioinfoDO();
        poi.setPoiType(item.getPoiType());
        poi.setPoiCode(item.getPoiCode());
        poi.setCityId(cityId);
        poi.setOrderNum(0);
        return poi;
    }

    public Integer queryPoiType(String poiCode) {
        CityPoiConfigItem item = tripConfig.getCityPoiConfigList().stream().filter(l -> StringUtils.equalsIgnoreCase(poiCode, l.getPoiCode())).findFirst().orElse(null);
        return item == null ? 0 : item.getPoiType();
    }

    /**
     * 查询供应商热门城市，使用默认兜底逻辑，只用于获取poi
     */
    public SeoHotVendorInformationDO queryVendorHotCityDefault(String vendorCode) {
        SeoHotVendorInformationDO result = queryVendorHotCity(vendorCode);
        //使用qconfig兜底，qconfig没配置使用LAX
        if (result != null) {
            return result;
        }
        VendorCityDto configDto = tripConfig.getVendorCityList().stream().filter(l -> StringUtils.equalsIgnoreCase(l.getVendorCode(), vendorCode)).findFirst().orElse(null);
        if (configDto != null && CollectionUtils.isNotEmpty(configDto.getCityIdList()) && StringUtils.isNotEmpty(configDto.getCityIdList().getFirst().getPoiCode())) {
            VendorCityItem item = configDto.getCityIdList().getFirst();
            SeoHotVendorInformationDO value = new SeoHotVendorInformationDO();
            value.setCityId(item.getCityId());
            value.setPoiType(item.getPoiType());
            value.setPoiCode(item.getPoiCode());
            value.setVendorCode(vendorCode);
            return value;
        }
        SeoHotVendorInformationDO value = new SeoHotVendorInformationDO();
        value.setCityId(347);
        value.setPoiType(1);
        value.setPoiCode("LAX");
        value.setVendorCode(vendorCode);
        return value;
    }

    /**
     * 查询供应商热门城市，使用默认兜底逻辑，只用于获取poi
     */
    public SeoHotVendorInformationDO queryVendorHotCityDefault(String vendorCode, Integer cityId) {
        SeoHotVendorInformationDO result = queryVendorCity(vendorCode, cityId);
        //使用qconfig兜底，qconfig没配置使用LAX
        if (result != null) {
            return result;
        }
        VendorCityDto configDto = tripConfig.getVendorCityList().stream().filter(l -> StringUtils.equalsIgnoreCase(l.getVendorCode(), vendorCode)).findFirst().orElse(null);
        VendorCityItem item = configDto == null || CollectionUtils.isEmpty(configDto.getCityIdList()) ? null
                : configDto.getCityIdList().stream().filter(l -> Objects.equals(l.getCityId(), cityId) && StringUtils.isNotEmpty(l.getPoiCode())).findFirst().orElse(null);
        if (item != null) {
            SeoHotVendorInformationDO value = new SeoHotVendorInformationDO();
            value.setCityId(item.getCityId());
            value.setPoiType(item.getPoiType());
            value.setPoiCode(item.getPoiCode());
            value.setVendorCode(vendorCode);
            return value;
        }
        SeoHotVendorInformationDO value = new SeoHotVendorInformationDO();
        value.setCityId(347);
        value.setPoiType(1);
        value.setPoiCode("LAX");
        value.setVendorCode(vendorCode);
        return value;
    }

    /**
     * 查询供应商热门城市，搜索量第一的城市
     */
    public SeoHotVendorInformationDO queryVendorHotCity(String vendorCode) {
        List<SeoHotVendorInformationDO> vendorCityList = seoVendorCache.queryVendorInformation(vendorCode);
        if (CollectionUtils.isEmpty(vendorCityList)) {
            return null;
        }
        Map<String, List<SeoHotVendorInformationDO>> vendorCityMap = vendorCityList.stream().collect(Collectors.groupingBy(l -> l.getVendorCode() + "_" + l.getCityId()));
        List<SeoHotVendorInformationDO> result = Lists.newArrayList();
        for (Map.Entry<String, List<SeoHotVendorInformationDO>> kv : vendorCityMap.entrySet()) {
            result.add(kv.getValue().get(0));
        }
        return result.stream().max(Comparator.comparing(SeoHotVendorInformationDO::getSearchNum)).orElse(null);
    }

    /**
     * 查询供应商top3热门城市，按搜索量排序
     */
    public List<SeoHotVendorInformationDO> queryVendorTop3City(String vendorCode) {
        List<SeoHotVendorInformationDO> vendorCityList = seoVendorCache.queryVendorInformation(vendorCode);
        if (CollectionUtils.isEmpty(vendorCityList)) {
            return Lists.newArrayList();
        }
        Map<String, List<SeoHotVendorInformationDO>> vendorCityMap = vendorCityList.stream().collect(Collectors.groupingBy(l -> l.getVendorCode() + "_" + l.getCityId()));
        List<SeoHotVendorInformationDO> result = Lists.newArrayList();
        Set<String> poiCodeSet = Sets.newHashSet();
        for (Map.Entry<String, List<SeoHotVendorInformationDO>> kv : vendorCityMap.entrySet()) {
            if (poiCodeSet.contains(kv.getValue().get(0).getPoiCode())) {
                continue;
            }
            poiCodeSet.add(kv.getValue().get(0).getPoiCode());
            result.add(kv.getValue().get(0));
        }
        return result.stream().sorted(Comparator.comparing(SeoHotVendorInformationDO::getSearchNum).reversed()).limit(3).collect(Collectors.toList());
    }

    /**
     * 查询供应商城市
     */
    public SeoHotVendorInformationDO queryVendorCity(String vendorCode, Integer cityId) {
        List<SeoHotVendorInformationDO> vendorCityList = seoVendorCache.queryVendorInformation(vendorCode);
        if (CollectionUtils.isEmpty(vendorCityList)) {
            return null;
        }
        List<SeoHotVendorInformationDO> cityList = vendorCityList.stream().filter(l -> Objects.equals(l.getCityId(), cityId)).collect(Collectors.toList());
        return cityList.stream().max(Comparator.comparing(SeoHotVendorInformationDO::getSearchNum)).orElse(null);
    }

    @NCache(holdMinute = 600)
    public String queryVehicleName(Long id, String locale) {
        SimpleStandardProductNameDTO simpleStandardProductNameDTO = carCommodityCommonServiceProxy.multiLanguageQuery(id, locale);
        if (simpleStandardProductNameDTO == null) {
            return null;
        }
        return simpleStandardProductNameDTO.getName();
    }

    public String queryUrlName(Long id, int type) {
        try {
            String urlName = "";
            if (type == 1) {
                Country country = countryRepository.findOne(id);
                urlName = country != null ? country.getEnglishName() : "";
            } else {
                City city = cityRepository.findOne(id);
                urlName = city != null ? city.getEnglishName() : "";
            }
            return urlName.toLowerCase().replace(" ", "-").replace("’", "").replace(".", "").replace("?", "");
        } catch (Exception e) {
            logger.warn("queryCountryUrlName", e);
            return "";
        }
    }

    public Integer getVendorCount(QuerySeoFaqRequestType request) {
        try {
            Integer countryId = request.getCountryId();
            //机场页面
            if (Optional.ofNullable(request.getPoiType()).orElse(0) > 0 && StringUtils.isNotEmpty(request.getPoiCode())) {
                List<SeoHotDestinatioinfoDO> airportList = queryDestinationByPoi(request.getPoiType(), request.getPoiCode());
                if (CollectionUtils.isEmpty(airportList)) {
                    return null;
                }
                countryId = airportList.getFirst().getCountryId();
            }
            //城市页面
            if (Optional.ofNullable(request.getCityId()).orElse(0) > 0) {
                SeoHotCityinfoDO cityinfoDO = queryHotCityByCity(request.getCityId());
                if (cityinfoDO == null) {
                    return null;
                }
                countryId = cityinfoDO.getCountryId();
            }
            if (Optional.ofNullable(countryId).orElse(0) > 0) {
                List<SeoHotInformationDO> list = seoInformationCache.queryInformationByCountry(countryId);
                return CollectionUtils.isEmpty(list) ? null : list.stream().map(SeoHotInformationDO::getVendorId).distinct().toList().size();
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    public Integer getVendorStoreCount(String vendorCode) {
        try {
            List<SeoHotVendorInformationDO> list = seoVendorCache.queryVendorInformation(vendorCode);
            return CollectionUtils.isEmpty(list) ? null : list.stream().map(SeoHotVendorInformationDO::getStoreNum).reduce(Integer::sum).orElse(null);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 活动top3供应商报价
     *
     * @param response
     * @return
     */
    public List<VendorVehicleDto> getTopVendor(QueryRecomdProductsResponseType response) {
        if (response == null || CollectionUtils.isEmpty(response.getRecomdProductResList()) || CollectionUtils.isEmpty(response.getRecomdProductResList().getFirst().getProducts())) {
            return Lists.newArrayList();
        }
        Set<String> vendorSet = Sets.newHashSet();
        //返回top3供应商
        return response.getRecomdProductResList().getFirst().getProducts().stream()
                .filter(l -> l.getVendorInfo() != null && StringUtils.isNotEmpty(l.getVendorInfo().getVendorName())
                        && StringUtils.isNotEmpty(l.getVendorInfo().getBizVendorCode()) && l.getVehicle() != null && l.getPrice() != null).map(l -> {
                    if (vendorSet.contains(l.getVendorInfo().getBizVendorCode())) {
                        return null;
                    }
                    vendorSet.add(l.getVendorInfo().getBizVendorCode());
                    VendorVehicleDto item = new VendorVehicleDto();
                    item.setVendorCode(l.getVendorInfo().getBizVendorCode());
                    item.setVendorName(l.getVendorInfo().getVendorName());
                    item.setCarType(l.getVehicle().getVehicleName());
                    item.setPrice(l.getPrice().getCurrentDailyPrice());
                    return item;
                }).filter(Objects::nonNull).limit(3).toList();
    }
}
